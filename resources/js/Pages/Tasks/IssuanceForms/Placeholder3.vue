<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import Button from 'primevue/button'
import Message from 'primevue/message'
import InputText from 'primevue/inputtext'
import { useForm } from '@inertiajs/vue3'
import FormMessage from '../../../Components/Form/FormMessage.vue'
import InputNumber from 'primevue/inputnumber'
import RadioButton from "primevue/radiobutton"
import DatePicker from "primevue/datepicker"
import Address from '../../../Components/Form/Address.vue'
import RealEstateRegistry from '../../../Components/Form/RealEstateRegistry.vue'

export default {
    components: {
        PipelineLayout,
        Button,
        Message,
        InputText,
        FormMessage,
        InputNumber,
        RadioButton,
        DatePicker,
        Address,
        RealEstateRegistry
    },
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },
    data() {
        // Recupera i dati di default dal task se presenti
        const initial = this.task?.data?.issuance?.[this.issuance.product.id] || {}

        return {
            form: useForm({
                // Base
                combinazione: null,
                intermediazione: null,
                importoIntermediazione: null,
                emissione: null,

                // Mutuo
                importoFinanziato: null,
                banca: '',
                durata: null,
                dataErogazione: null,
                notaio: '',

                // Dati immobile
                tipoAbitazione: null,
                indirizzoImmobile: {
                    type: 'residence',
                    street: '',
                    number: '',
                    zip: '',
                    city: '',
                    province: '',
                    region: '',
                    country: 'IT',
                },
                piano: null,
                interno: null,

                // Dati catastali
                datiCatastali: {
                    foglio: '',
                    part: '',
                    sub: '',
                    cat: '',
                    classe: '',
                    consist: '',
                    rendita: null
                }
            }),
            loading: false,
        }
    },
    methods: {
        submit() {
            this.loading = true;

            console.log(this.form)
            return;

            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + issuance.product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ issuance.product.name }}
                                <p class="text-gray-500 text-sm font-normal">Form di adesione</p>
                            </span>
                        </h2>

                        <hr class="my-4">

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Seleziona la combinazione</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-1" name="combinazione" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-1">Combinazione 1</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-3" name="combinazione" value="3" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-3">Combinazione 3</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-5" name="combinazione" value="5" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-5">Combinazione 5</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-6" name="combinazione" value="6" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-6">Combinazione 6</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-8" name="combinazione" value="8" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-8">Combinazione 8</label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Intermediazione:</div>
                            <div class="mt-1 space-x-6">
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.intermediazione" inputId="interm-1" name="intermediazione" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="interm-1">Si</label>
                                </div>
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.intermediazione" inputId="interm-0" name="intermediazione" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="interm-0">No</label>
                                </div>
                            </div>
                        </div>

                        <div v-if="form.intermediazione === '1'">

                            <div class="grid grid-cols-2 gap-x-5 gap-y-8">
                                <div class="col-span-1">
                                    <InputNumber
                                        v-model="form.importoIntermediazione"
                                        class="w-full"
                                        mode="currency"
                                        currency="EUR"
                                        locale="it-IT"
                                        placeholder="Intermediazione"
                                    />
                                    <FormMessage :errors="form.errors" :field="`${form.errors}.importoIntermediazione`">
                                        Inserisci l'importo di intermediazione
                                    </FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputNumber
                                        v-model="form.emissione"
                                        class="w-full"
                                        mode="currency"
                                        currency="EUR"
                                        locale="it-IT"
                                        placeholder="Diritti di emissione"
                                    />
                                    <FormMessage :errors="form.errors" :field="`${form.errors}.emissione`">
                                        Inserisci i diritti di emissione
                                    </FormMessage>
                                </div>
                            </div>

                        </div>

                        <div class="section-header mb-2 mt-14">Dati mutuo:</div>

                        <div class="grid grid-cols-2 gap-x-5 gap-y-8">
                            <div class="col-span-1">
                                <InputNumber
                                    v-model="form.importoFinanziato"
                                    class="w-full"
                                    mode="currency"
                                    currency="EUR"
                                    locale="it-IT"
                                    placeholder="Importo finanziato"
                                />
                            </div>
                            <div class="col-span-1">
                                <InputText
                                    v-model="form.banca"
                                    class="w-full"
                                    placeholder="Banca erogante"
                                />
                            </div>
                            <div class="col-span-1">
                                <InputNumber
                                    v-model="form.durata"
                                    class="w-full"
                                    placeholder="Durata"
                                />
                            </div>
                            <div class="col-span-1">
                                <DatePicker placeholder="Data erogazione" v-model="form.dataErogazione" dateFormat="dd/mm/yy" />
                            </div>
                            <div class="col-span-1">
                                <InputText
                                    v-model="form.notaio"
                                    class="w-full"
                                    placeholder="Notaio"
                                />
                            </div>
                        </div>

                        <div class="section-header mb-2 mt-14">Dati immobile da assicurare:</div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Tipo abitazione</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.tipoAbitazione" inputId="tipoAbit-1" name="tipoAbitazione" value="appartamento" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="tipoAbit-1">Appartamento in condominio</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.tipoAbitazione" inputId="tipoAbit-2" name="tipoAbitazione" value="villa" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="tipoAbit-2">Villa, villetta anche plurifamiliare con ingresso singolo</label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900 mb-4">Indirizzo immobile</div>
                            <Address
                                :address="form.indirizzoImmobile"
                                :errors="{messages: form.errors, key: 'indirizzoImmobile'}"
                                type="residence"
                            />
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-8">
                            <div class="col-span-1">
                                <InputNumber
                                    v-model="form.piano"
                                    placeholder="Piano"
                                    class="w-full"
                                />
                            </div>
                            <div class="col-span-1">
                                <InputText
                                    v-model="form.interno"
                                    placeholder="Interno"
                                    class="w-full"
                                />
                            </div>
                        </div>

                        <div class="font-semibold leading-6 text-gray-900 mb-4">Dati Catastali</div>

                        <RealEstateRegistry
                            :cadastralData="form.datiCatastali"
                            :errors="{messages: form.errors, key: 'datiCatastali'}"
                        />

                        <div class="mt-6">
                            <Button type="submit" label="Avanti" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>
